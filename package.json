{"name": "glbiashara-solution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "next": "15.4.2", "next-cloudinary": "^6.16.0", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}