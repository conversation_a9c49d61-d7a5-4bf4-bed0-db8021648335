'use client'

interface MediaDisplayProps {
  mediaUrls: string[]
  title: string
}

export function MediaDisplay({ mediaUrls, title }: MediaDisplayProps) {
  if (!mediaUrls || mediaUrls.length === 0) {
    return null
  }

  return (
    <div className="mb-4">
      {mediaUrls.length === 1 ? (
        // Single media item
        <div className="aspect-video w-full overflow-hidden rounded-lg bg-neutral-200 dark:bg-neutral-700">
          {mediaUrls[0].includes('.mp4') || mediaUrls[0].includes('.webm') || mediaUrls[0].includes('.mov') ? (
            <video
              src={mediaUrls[0]}
              className="h-full w-full object-cover"
              controls
              preload="metadata"
            />
          ) : (
            <img
              src={mediaUrls[0]}
              alt="Post media"
              className="h-full w-full object-cover"
              onError={(e) => {
                console.error('Failed to load image:', mediaUrls[0])
                const target = e.target as HTMLImageElement
                target.style.display = 'none'
                const parent = target.parentElement
                if (parent) {
                  parent.innerHTML = `
                    <div class="flex h-full w-full items-center justify-center bg-neutral-200 dark:bg-neutral-700 text-neutral-500">
                      <div class="text-center">
                        <svg class="h-12 w-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <p class="text-xs">Image failed to load</p>
                        <p class="text-xs text-neutral-400 mt-1">URL: ${mediaUrls[0].substring(0, 50)}...</p>
                      </div>
                    </div>
                  `
                }
              }}
              onLoad={() => {
                console.log('Image loaded successfully:', mediaUrls[0])
              }}
            />
          )}
        </div>
      ) : (
        // Multiple media items - grid layout
        <div className={`grid gap-2 ${
          mediaUrls.length === 2 ? 'grid-cols-2' :
          mediaUrls.length === 3 ? 'grid-cols-2' :
          'grid-cols-2'
        }`}>
          {mediaUrls.slice(0, 4).map((url, index) => (
            <div
              key={index}
              className={`aspect-square overflow-hidden rounded-lg bg-neutral-200 dark:bg-neutral-700 relative ${
                mediaUrls.length === 3 && index === 0 ? 'row-span-2' : ''
              }`}
            >
              {url.includes('.mp4') || url.includes('.webm') || url.includes('.mov') ? (
                <video
                  src={url}
                  className="h-full w-full object-cover"
                  preload="metadata"
                />
              ) : (
                <img
                  src={url}
                  alt={`Post media ${index + 1}`}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    console.error('Failed to load grid image:', url)
                    const target = e.target as HTMLImageElement
                    target.style.display = 'none'
                    const parent = target.parentElement
                    if (parent) {
                      parent.innerHTML = `
                        <div class="flex h-full w-full items-center justify-center bg-neutral-200 dark:bg-neutral-700 text-neutral-500">
                          <div class="text-center">
                            <svg class="h-8 w-8 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <p class="text-xs">Failed</p>
                          </div>
                        </div>
                      `
                    }
                  }}
                  onLoad={() => {
                    console.log('Grid image loaded successfully:', url)
                  }}
                />
              )}
              {mediaUrls.length > 4 && index === 3 && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    +{mediaUrls.length - 4} more
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
